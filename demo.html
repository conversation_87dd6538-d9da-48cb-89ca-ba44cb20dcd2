<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FunShot - Kebahagiaan yang tidak diketahui orang lain</title>
    <meta name="description" content="Unduh FunShot, platform drama pendek dengan Neymar sebagai duta global. Tonton & hasilkan F-Bean sekarang!">
    
    <!-- Facebook Meta Tags -->
    <meta property="og:url" content="https://f-s.fun/">
    <meta property="og:type" content="website">
    <meta property="og:title" content="FunShot - Kebahagiaan yang tidak diketahui orang lain">
    <meta property="og:description" content="Unduh FunShot, platform drama pendek dengan Neymar sebagai duta global. Tonton & hasilkan F-Bean sekarang!">
    
    <link rel="icon" href="./static/image/logo1x.png" />
    <script src="https://res.openinstall.com/openinstall.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #000000;
            color: white;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .header {
            background-color: #000;
            padding: 20px 0;
            border-bottom: 1px solid #262626;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .app-info h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .app-info p {
            font-size: 14px;
            color: #8E8E92;
        }
        
        .contact-btn {
            background-color: #BAFC51;
            color: #000;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .contact-btn:hover {
            background-color: #a8e045;
        }
        
        .hero-section {
            text-align: center;
            padding: 80px 0;
            background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
        }
        
        .hero-title {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .hero-title .highlight {
            color: #BAFC51;
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 50px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .download-section {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 50px;
        }
        
        .download-btn {
            background-color: #BAFC51;
            color: #000;
            padding: 15px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            font-size: 18px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .download-btn:hover {
            background-color: #a8e045;
            transform: translateY(-2px);
        }
        
        .steps-section {
            padding: 80px 0;
            background-color: #111;
        }
        
        .steps-title {
            text-align: center;
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .steps-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .steps-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 40px;
        }
        
        .step {
            text-align: center;
            max-width: 300px;
        }
        
        .step-number {
            background-color: #BAFC51;
            color: #000;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin: 0 auto 20px;
        }
        
        .step h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .step p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }
        
        .features-section {
            padding: 80px 0;
            background-color: #000;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }
        
        .feature-card {
            background-color: #1a1a1a;
            padding: 30px;
            border-radius: 15px;
            border: 1px solid #262626;
        }
        
        .feature-card h3 {
            color: #BAFC51;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .feature-card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }
        
        .footer {
            background-color: #111;
            padding: 60px 0 30px;
            border-top: 1px solid #262626;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }
        
        .footer-section h4 {
            color: #BAFC51;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }
        
        .footer-section p, .footer-section a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            margin-bottom: 10px;
            display: block;
        }
        
        .footer-section a:hover {
            color: #BAFC51;
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #262626;
            color: rgba(255, 255, 255, 0.5);
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.2rem;
            }
            
            .steps-title {
                font-size: 2rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .download-section {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">
                        <img src="./static/image/logo3x.png" alt="FunShot Logo">
                    </div>
                    <div class="app-info">
                        <h1>Funshot App</h1>
                        <p>Video 'New' Generation</p>
                    </div>
                </div>
                <a href="https://linktr.ee/funshot_io" class="contact-btn" target="_blank">Contact Us</a>
            </div>
        </div>
    </header>

    <main>
        <section class="hero-section">
            <div class="container">
                <h1 class="hero-title">
                    Incentive <span class="highlight">Worldwide</span>
                </h1>
                <p class="hero-subtitle">
                    Watch short videos, invite friends, and complete tasks on your mobile to earn F-Bean, easily participating in the Web3 value network.
                </p>
                
                <div class="download-section">
                    <a href="#" class="download-btn" onclick="handleDownload()">
                        📱 Download for Android
                    </a>
                    <a href="#" class="download-btn" onclick="handleDownload()">
                        🍎 Download for iOS
                    </a>
                </div>
            </div>
        </section>

        <section class="steps-section">
            <div class="container">
                <h2 class="steps-title">Start Your Funshot Journey</h2>
                <p class="steps-subtitle">
                    Just 3 steps to experience exciting new ways to enjoy short dramas and earn rewards. Watch shows, complete tasks, invite friends, and earn F-Bean every day!
                </p>
                
                <div class="steps-container">
                    <div class="step">
                        <div class="step-number">01</div>
                        <h3>Create an Account</h3>
                        <p>Sign up quickly and easily to start your journey with FunShot</p>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">02</div>
                        <h3>Start Watching</h3>
                        <p>Enjoy premium short dramas and videos from around the world</p>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">03</div>
                        <h3>Earn F-Bean</h3>
                        <p>Get rewarded for watching, sharing, and inviting friends</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="features-section">
            <div class="container">
                <h2 class="steps-title">Why Choose FunShot?</h2>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <h3>🎬 Free Global Short Dramas</h3>
                        <p>Access premium content from Europe, America, Japan, and Korea. No membership needed, no ads, truly free entertainment.</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>⚽ Neymar Global Ambassador</h3>
                        <p>Global soccer star Neymar Jr. officially endorses FunShot, enhancing our international brand presence.</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>🎯 TikTok-Level Video Ecosystem</h3>
                        <p>Millions of creators producing short dramas, vlogs, tutorials, games, beauty, food, fitness content and more.</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>💰 Unique F-Bean Earning System</h3>
                        <p>Earn F-Bean tokens through watching, sharing, and creating. A deflationary model with real value and multiple use cases.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>About Us</h4>
                    <p>Funshot Introduction</p>
                    <p>User Rights and Safety</p>
                </div>
                
                <div class="footer-section">
                    <h4>Ecosystem Links</h4>
                    <p>F-Bean Mechanism</p>
                    <p>Influencer Cooperation Plan</p>
                </div>
                
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <a href="https://linktr.ee/funshot_io" target="_blank">Twitter</a>
                    <a href="https://linktr.ee/funshot_io" target="_blank">Instagram</a>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>Privacy Policy | Copyright © 2025 Funshot</p>
            </div>
        </div>
    </footer>

    <script>
        function handleDownload() {
            if (typeof OpenInstall !== 'undefined') {
                var data = OpenInstall.parseUrlParams();
                new OpenInstall({
                    appKey: "l23zgl",
                    onready: function() {
                        var m = this;
                        m.schemeWakeup();
                        m.wakeupOrInstall();
                    }
                }, data);
            } else {
                alert('Download functionality will be available soon!');
            }
        }
        
        // 检测设备类型并添加相应的类名
        function checkDevice() {
            const width = window.innerWidth;
            const body = document.body;
            
            if (width < 960) {
                body.classList.add('mobile');
            } else {
                body.classList.add('pc');
            }
        }
        
        // 页面加载时检测设备
        window.addEventListener('load', checkDevice);
        window.addEventListener('resize', checkDevice);
    </script>
</body>
</html>
